---
description: 
globs: 
alwaysApply: true
---
# UI/UX与CSS布局规范

本项目UI设计和开发遵循以下规范，以确保页面美观、一致且具有良好的用户体验。

## 布局规范

### Flex布局优先

- **优先使用Flex布局**进行页面和组件排列
- 优先使用项目预定义的Flex工具类:
  - `flex-x`: 水平排列
  - `flex-y`: 垂直排列
  - `flex-x-center`: 水平排列并垂直居中
  - `flex-x-between`: 水平排列、两端对齐、垂直居中
  - `flex-y-center`: 垂直排列并水平居中

```html
<view class="flex-x-between p-4">
  <text>左侧内容</text>
  <text>右侧内容</text>
</view>
```

### 间距规范

- 优先使用预定义的间距变量，保持统一性
- 间距使用4的倍数(8rpx, 16rpx, 24rpx, 32rpx等)
- 对应类名: `spacing-4`, `spacing-8`, `spacing-12`等

### 圆角规范

- 小圆角: `border-radius-sm` (2px)
- 中圆角: `border-radius-base` (3px)
- 大圆角: `border-radius-lg` (6px)
- 圆形: `border-radius-circle` (50%)

## 样式开发规范

### SCSS用法

- 优先使用嵌套语法增强可读性
- 优先使用变量(`$`前缀)替代硬编码值
- 优先使用[uni.scss](mdc:src/uni.scss)中预定义的变量，如果未定义且频繁使用可加入到uni.scss中

```scss
.card {
  background-color: #fff;
  border-radius: $border-radius-base;
  
  .card-header {
    color: $text-main;
    padding: $spacing-8;
  }
  
  .card-content {
    color: $text-info;
    padding: $spacing-10;
  }
}
```

### 颜色使用

- **严格遵循**设计系统中的颜色规范
- 文本颜色使用预设变量: `$text-main`, `$text-info`, `$text-grey`等
- 背景使用`$bg-color`或白色
- 强调色使用`$primary`系列

## 组件使用规范

### 组件选用优先级

1. **优先使用已有UI组件库**
   - uni-ui组件: `<uni-forms>`, `<uni-easyinput>`, `<uni-popup>`等
   - ThorUI组件: `<tui-button>`, `<tui-list-view>`, `<tui-card>`等

2. **复用项目内现有组件**
   - 检查`components/`目录中是否有满足需求的组件

3. **创建新组件**
   - 仅在现有组件无法满足需求时创建新组件
   - 遵循项目命名规范
   - 保持组件简洁，单一职责

## 页面美化技巧

### 提升页面层次感

- 使用卡片、阴影和间距创建视觉层次
- 轻微的阴影效果: `box-shadow: 0 2px 8px rgba(0,0,0,0.05)`
- 卡片内容使用不同的背景色和边距区分区域

### 提升视觉质感

- 适当使用过渡动画: `transition: all 0.3s`
- 交互元素添加状态变化(hover, active)
- 重要信息使用对比色或加粗强调
- 图标和文字搭配使用增强可读性

### 响应式设计

- 使用rpx单位适配不同屏幕
- 避免固定宽度，使用百分比或flex布局
- 文本字号不宜过小，最小建议28rpx (14px)

## 实践案例

### 卡片组件示例

```vue
<template>
  <view class="card flex-y spacing-16 bg-white">
    <view class="card-header flex-x-between px-4 py-3">
      <text class="ft16 text-main font-bold">卡片标题</text>
      <text class="ft14 text-grey">详情</text>
    </view>
    <view class="card-divider"></view>
    <view class="card-content px-4 py-3">
      <text class="ft14 text-info">卡片内容区域</text>
    </view>
  </view>
</template>

<style lang="scss">
.card {
  border-radius: $border-radius-base;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  
  .card-divider {
    height: 1px;
    background-color: #f5f5f5;
    width: 100%;
  }
}
</style>
```

遵循以上规范将确保页面美观、一致、高效且具有良好的用户体验。
