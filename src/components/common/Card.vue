<template>
  <view class="custom-card" :style="cardStyle">
    <slot></slot>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface Props {
  margin?: string;
  padding?: string;
  borderRadius?: string;
  backgroundColor?: string;
  shadow?: boolean; // 是否显示阴影
}

const props = withDefaults(defineProps<Props>(), {
  margin: "0 20rpx 20rpx 20rpx",
  padding: "32rpx",
  borderRadius: "16rpx",
  backgroundColor: "#FFFFFF",
  shadow: true,
});

const cardStyle = computed(() => {
  let style = `
    margin: ${props.margin};
    padding: ${props.padding};
    border-radius: ${props.borderRadius};
    background-color: ${props.backgroundColor};
  `;
  if (props.shadow) {
    style += "box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);";
  }
  return style;
});
</script>

<style lang="scss" scoped>
.custom-card {
  width: auto; // 默认宽度自适应内容，可根据需要调整或通过外部类覆盖
  overflow: hidden; // 确保圆角生效
}
</style>
