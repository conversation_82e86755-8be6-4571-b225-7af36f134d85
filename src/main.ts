import 'virtual:uno.css'
import uvUI from '@climblee/uv-ui'
import { createSSRApp } from "vue";
import App from "./App.vue";
import NetworkImage from "./components/NetworkImage.vue";
import CustomNavBar from "./components/CustomNavBar.vue";
import Card from "./components/common/Card.vue";

export function createApp() {
  const app = createSSRApp(App);
  app.use(uvUI);
  // 全局注册组件
  app.component('NetworkImage', NetworkImage);
  app.component('CustomNavBar', CustomNavBar);
  app.component('Card', Card);

  return {
    app,
  };
}
