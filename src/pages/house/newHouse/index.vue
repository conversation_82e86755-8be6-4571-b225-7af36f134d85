<template>
  <view class="container">
    <!-- 内容区域 - 使用z-paging组件 -->
    <z-paging
      ref="pagingRef"
      v-model="houseList"
      @query="queryHouseList"
      empty-view-text="暂无房源数据"
    >
      <!-- 搜索栏 -->
      <view class="search-section flex-x-between" v-show="showSearchBar">
        <view class="search-bar flex-x" @tap="handleSearch">
          <text class="i-carbon-search text-gray-400 text-32rpx"></text>
          <text class="search-placeholder">搜索楼盘名称、位置</text>
        </view>
      </view>

      <!-- 筛选组件 -->
      <view class="filter-container">
        <FilterPanel
          @filter-change="handleFilterChange"
          @open-filter="openFilter"
          @close-filter="closeFilter"
        />
      </view>
      <view
        v-for="house in houseList"
        :key="house.id"
        @tap="navigateToDetail(house.id)"
      >
        <CustomCard padding="0 0 32rpx 0">
          <!-- 房源图片 -->
          <view class="house-image-wrapper">
            <image
              :src="house.image"
              mode="aspectFill"
              class="house-image"
              :lazy-load="true"
            />
            <!-- 状态标签 -->
            <view
              v-if="house.status"
              class="status-badge"
              :class="getStatusClass(house.status)"
            >
              {{ house.status }}
            </view>
          </view>

          <!-- 房源信息 -->
          <view class="house-content">
            <!-- 标题和房源类型 -->
            <view class="house-header">
              <text class="house-title">{{ house.title }}</text>
              <view class="property-type-badge">{{
                house.propertyType || "住宅"
              }}</view>
            </view>

            <!-- 价格区域 -->
            <view class="price-area">
              <text
                class="house-price"
                :class="{ 'price-pending': house.price === '价格待定' }"
              >
                {{ house.price }}
              </text>
              <view v-if="house.specialOffer" class="special-tag">
                {{ house.specialOffer }}
              </view>
            </view>

            <!-- 位置信息 -->
            <text class="house-location">{{ house.location }}</text>

            <!-- 标签区域 -->
            <view v-if="house.tags && house.tags.length" class="tags-container">
              <text v-for="tag in house.tags" :key="tag" class="info-tag">
                {{ tag }}
              </text>
            </view>

            <!-- 关注信息 -->
            <view v-if="house.attentionCount" class="attention-text">
              近期有{{ house.attentionCount }}人关注该楼盘
            </view>
          </view>
        </CustomCard>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import type { NewHouseItem } from "@/types/house";
import FilterPanel from "./components/FilterPanel.vue";
import CustomCard from "@/components/common/Card.vue";

// z-paging引用
const pagingRef = ref(null);

// 当前页码
const showSearchBar = ref(true);

// 响应式数据
const currentLocation = ref("北京");
const showFilterModal = ref(false);

// 筛选条件
const currentFilters = reactive({
  area: "",
  price: "",
  houseType: "",
  features: [] as string[],
  developer: "",
  decoration: "",
});

// 选中的筛选条件
const selectedFilters = reactive({
  price: "",
  houseType: "",
  features: [] as string[],
});

// 保存原始房源列表数据
const originalHouseList = [
  {
    id: "1",
    image: "https://picsum.photos/seed/house-1/300/200",
    title: "元垄石景山",
    location: "石景山·石景山其它/建面120-196㎡/3,4居",
    price: "75000元/平",
    specialOffer: "有15套特价房",
    tags: ["近地铁", "综合商场", "公园"],
    attentionCount: 30,
    status: "在售",
    priceRange: "总价950-1560万",
    propertyType: "住宅",
  },
  {
    id: "2",
    image: "https://picsum.photos/seed/house-2/300/200",
    title: "招商云璟",
    location: "通州·梨园/建面79-128㎡/3,4居",
    price: "60000元/平",
    specialOffer: "有9套特价房",
    tags: ["近地铁", "医疗配套", "期房"],
    attentionCount: 33,
    status: "在售",
    priceRange: "总价470-830万",
    propertyType: "住宅",
  },
  {
    id: "smds",
    image: "https://picsum.photos/seed/house-smds/300/200",
    title: "世茂维拉左右间",
    location: "房山·长阳",
    price: "价格待定",
    specialOffer: "",
    tags: ["近地铁", "综合商场", "小型社区"],
    attentionCount: 10,
    status: "待售",
    priceRange: "",
    propertyType: "写字楼",
  },
  {
    id: "cayh",
    image: "https://picsum.photos/seed/house-cayh/300/200",
    title: "长安运河",
    location: "通州·万达",
    price: "价格待定",
    specialOffer: "",
    tags: ["近地铁", "综合商场", "三甲医院"],
    attentionCount: 5,
    status: "待售",
    priceRange: "",
    propertyType: "商业类",
  },
  {
    id: "3",
    image: "https://picsum.photos/seed/house-3/300/200",
    title: "京投发展森与天成",
    location: "丰台·新宫/建面55-148㎡/1,2,3,4居",
    price: "78000元/平",
    specialOffer: "",
    tags: ["近地铁", "公园", "期房"],
    attentionCount: 0,
    status: "在售",
    priceRange: "总价430-1150万",
    propertyType: "住宅",
  },
  {
    id: "4",
    image: "https://picsum.photos/seed/house-4/300/200",
    title: "清樾府",
    location: "昌平·沙河/建面86-143㎡/3,4居",
    price: "46000元/平",
    specialOffer: "",
    tags: ["期房", "小三居", "低密居所"],
    attentionCount: 39,
    status: "在售",
    priceRange: "总价410-680万",
    propertyType: "住宅",
  },
];

// 热门楼盘数据
const hotHouses = ref([
  {
    id: "h1",
    image: "https://picsum.photos/seed/hot-1/300/200",
    title: "首开龙湖·云著",
    price: "62000元/平",
    tags: ["品牌开发商", "低密度", "热销楼盘"],
  },
  {
    id: "h2",
    image: "https://picsum.photos/seed/hot-2/300/200",
    title: "华润西山金茂府",
    price: "85000元/平",
    tags: ["品牌开发商", "地铁房", "公园"],
  },
  {
    id: "h3",
    image: "https://picsum.photos/seed/hot-3/300/200",
    title: "万科蓝山",
    price: "58000元/平",
    tags: ["品牌开发商", "低总价", "精装修"],
  },
  {
    id: "h4",
    image: "https://picsum.photos/seed/hot-4/300/200",
    title: "朗润园",
    price: "53000元/平",
    tags: ["低总价", "地铁房", "配套齐全"],
  },
]);

// 房源列表数据
const houseList = ref<NewHouseItem[]>([]);

// 方法
const navigateBack = () => {
  uni.navigateBack();
};

const handleSearch = () => {
  uni.showToast({
    title: "搜索功能开发中",
    icon: "none",
  });
};

const selectLocation = () => {
  uni.showActionSheet({
    itemList: ["北京", "上海", "广州", "深圳", "杭州"],
    success: (res) => {
      const locations = ["北京", "上海", "广州", "深圳", "杭州"];
      currentLocation.value = locations[res.tapIndex];
      // 切换城市后重新加载数据
      if (pagingRef.value) {
        pagingRef.value.reload();
      }
    },
  });
};

// 查看更多热门楼盘
const viewMoreHotHouse = () => {
  uni.showToast({
    title: "查看更多热门楼盘",
    icon: "none",
  });
};

// 查看更多开盘日历
const viewMoreCalendar = () => {
  uni.showToast({
    title: "查看更多开盘日历",
    icon: "none",
  });
};

// 预约看房
const appointHouse = (id: string) => {
  uni.showToast({
    title: "预约看房功能开发中",
    icon: "none",
  });
};

const getStatusClass = (status: string) => {
  switch (status) {
    case "在售":
      return "status-selling";
    case "待售":
      return "status-pending-sale";
    case "热销":
      return "status-hot";
    case "即将开盘":
      return "status-coming";
    case "售罄":
      return "status-sold";
    case "二手房":
      return "status-second";
    default:
      return "status-default";
  }
};

const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/newHouse/detail?id=${id}`,
  });
};

// z-paging查询数据方法
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 这里模拟分页数据请求
  houseList.value = originalHouseList;
  pagingRef.value.complete(houseList.value);
};

// 打开筛选框
const openFilter = () => {
  showSearchBar.value = false;
};

// 关闭筛选框
const closeFilter = () => {
  showSearchBar.value = true;
};

// 处理筛选变化
const handleFilterChange = (filters: any) => {
  showSearchBar.value = true;
  Object.assign(currentFilters, filters);
  console.log("筛选条件已更新:", currentFilters);

  // 重新加载数据
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

onMounted(() => {
  // 页面加载时的初始化操作
  console.log("页面已加载");
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  /* 搜索栏样式 */
  .search-section {
    padding: 24rpx 32rpx;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .search-bar {
    height: 72rpx;
    padding: 0 24rpx;
    background-color: #f8f9fa;
    border-radius: 36rpx;
    gap: 16rpx;
  }

  .search-placeholder {
    color: #999;
    font-size: 28rpx;
  }

  /* 筛选栏样式 */
  .filter-container {
    position: sticky;
    top: 0;
    z-index: 100;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .section-header {
    margin-bottom: 20rpx;
  }

  .section-title {
    display: flex;
    flex-direction: column;

    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      margin-bottom: 8rpx;
    }

    .sub-title {
      font-size: 24rpx;
      color: #999;
    }
  }

  .more-btn {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    color: #3b7fff;
  }

  /* 热门楼盘样式 */
  .hot-houses-scroll {
    width: 100%;
  }

  .hot-houses-container {
    padding: 10rpx 0;
    gap: 0;
  }

  .hot-house-item-card {
    display: inline-block;
    width: 280rpx;
    &:last-child {
      margin-right: 0 !important;
    }
  }

  .hot-house-item-content {
    width: 100%;

    .hot-house-image {
      width: 100%;
      height: 180rpx;
    }

    .hot-house-info {
      padding: 16rpx;
      background-color: #fff;

      .hot-house-name {
        font-size: 28rpx;
        font-weight: 500;
        margin-bottom: 8rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: block;
      }

      .hot-house-price {
        font-size: 26rpx;
        color: #ff5a5f;
        font-weight: 500;
      }
    }
  }

  .house-image-wrapper {
    position: relative;
    width: 100%;
    height: 320rpx;
  }

  .house-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .status-badge {
    position: absolute;
    top: 16rpx;
    left: 16rpx;
    padding: 6rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    font-weight: 500;
    color: #fff;
  }

  .status-selling {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .status-pending-sale {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .status-hot {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .status-coming {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .status-sold {
    background: linear-gradient(135deg, #a8a8a8 0%, #8c8c8c 100%);
  }

  .house-content {
    padding: 24rpx;
  }

  .house-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16rpx;
  }

  .house-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #222;
    line-height: 1.4;
    flex: 1;
    margin-right: 16rpx;
  }

  .property-type-badge {
    padding: 4rpx 12rpx;
    background: #f0f0f0;
    border-radius: 16rpx;
    font-size: 22rpx;
    color: #666;
    white-space: nowrap;
  }

  .price-area {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
    flex-wrap: wrap;
    gap: 12rpx;
  }

  .house-price {
    font-size: 36rpx;
    font-weight: 700;
    color: #ff4757;
  }

  .price-pending {
    color: #ff6b35;
  }

  .special-tag {
    padding: 4rpx 12rpx;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-radius: 16rpx;
    font-size: 20rpx;
    color: #ff4757;
    font-weight: 500;
  }

  .house-location {
    font-size: 26rpx;
    color: #666;
    line-height: 1.4;
    margin-bottom: 16rpx;
    display: block;
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    margin-bottom: 12rpx;
  }

  .info-tag {
    padding: 6rpx 12rpx;
    background: #f8f9fa;
    border: 1rpx solid #e9ecef;
    border-radius: 16rpx;
    font-size: 22rpx;
    color: #495057;
  }

  .attention-text {
    font-size: 24rpx;
    color: #ff4757;
    font-weight: 500;
  }
}
</style>
