:root {
    --bondi-blue: #0897b4;
    --hermes-orange: #ff6a01;
}

.padding-sm {
    padding: 12rpx;
}

.padding-md,
.padding {
    padding: 24rpx;
}

.padding-lg {
    padding: 32rpx;
}

.radius-sm {
    border-radius: 8rpx;
}

.radius-md,
.radius {
    border-radius: 16rpx;
}

.radius-lg {
    border-radius: 24rpx;
}

.radius-xl {
    border-radius: 32rpx;
}

.radius-xxl {
    border-radius: 40rpx;
}

.blue-color {
    color: var(--bondi-blue);
}

.orange-color {
    color: var(--hermes-orange);
}

view,
text,
image,
button,
input,
textarea,
scroll-view {
    box-sizing: border-box;
}

image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* 使用gpu加速 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

button {
    border: none;
}

button::after {
    border: none;
}

page,
body {
    background-color: #f5f5f5;
    font-family:
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        Roboto,
        Oxygen,
        Ubuntu,
        Cantarell,
        "Open Sans",
        "Helvetica Neue",
        sans-serif;
}

.container {
    background-color: #f5f5f5;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.wrapper {
    flex: 1;
    background-color: #fff;
}

.card {
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    margin: 20rpx;
}
